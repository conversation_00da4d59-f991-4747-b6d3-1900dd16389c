services:
  postgres:
    container_name: postgres
    image: postgres:15
    networks:
      - evolution-net
    command: ["postgres", "-c", "max_connections=1000"]
    restart: always
    ports:
      - 5432:5432
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=evolution
      - POSTGRES_DB=evolution
    volumes:
      - postgres_data:/var/lib/postgresql/data
    expose:
      - 5432

  pgadmin:
    image: dpage/pgadmin4:latest
    networks:
      - evolution-net
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=root123  
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - 4000:80
    links:
      - postgres
  redis:
    image: redis:latest
    networks:
      - evolution-net
    container_name: redis
    command: >
      redis-server --port 6379 --appendonly yes
    volumes:
      - evolution_redis:/data
    ports:
      - 6379:6379
  evolution-api:
    container_name: evolution_api
    image: atendai/evolution-api:v2.1.1
    restart: always
    networks:
      - evolution-net
    ports:
      - "8080:8080"
    env_file:
      - .env
    volumes:
      - evolution_instances:/evolution/instances
    depends_on:
      - redis
      - postgres

volumes:
  evolution_instances:
  evolution_redis:
  postgres_data:
  pgadmin_data:


networks:
  evolution-net:
    name: evolution-net
    driver: bridge